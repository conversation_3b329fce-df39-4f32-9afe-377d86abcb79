/* 
 * Custom styles for the AI Assistant homepage
 * These styles complement Polaris components with additional customizations
 */

/* Hero section video container */
.videoContainer {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.videoContainer:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Plan cards hover effects */
.planCard {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  height: 100%;
}

.planCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

/* Popular plan highlight */
.popularPlan {
  position: relative;
  border: 2px solid #2563eb;
}

.popularPlan::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  border-radius: 8px;
  z-index: -1;
}

/* Support section cards */
.supportCard {
  transition: transform 0.2s ease-in-out;
  height: 100%;
}

.supportCard:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .videoContainer {
    max-width: 100%;
    margin: 0 1rem;
  }
  
  .planCard {
    margin-bottom: 1rem;
  }
}

/* Custom button styles for CTAs */
.primaryCta {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  transition: all 0.2s ease-in-out;
}

.primaryCta:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
  transform: translateY(-1px);
}

/* Feature list styling */
.featureList {
  list-style: none;
  padding: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Accessibility improvements */
.skipLink {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skipLink:focus {
  top: 6px;
}

/* Focus indicators for better accessibility */
.focusable:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Animation for chat button */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
  }
}

.chatButton {
  animation: pulse 2s infinite;
}

/* Grid layout improvements */
.responsiveGrid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 640px) {
  .responsiveGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
