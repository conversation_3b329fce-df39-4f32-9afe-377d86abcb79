import { useState, useRef, useEffect } from "react";
import {
  Card,
  TextField,
  Button,
  Badge,
  Icon,
  Text,
  BlockStack,
  InlineStack,
  Box,
  Scrollable,
  Divider,
} from "@shopify/polaris";
import {
  ChatIcon,
  SendIcon,
  AttachmentIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
} from "@shopify/polaris-icons";
import styles from "./ChatbotSidebar.module.css";

// Define the structure for chat messages
interface ChatMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
}

// Define the user mode type
type UserMode = "free" | "pro" | "developer";

/**
 * ChatbotSidebar Component
 * 
 * A fixed sidebar that acts as a Shopify Copilot, always available to merchants
 * regardless of which page they're on in the Shopify admin.
 * 
 * Features:
 * - Fixed positioning to the right side of the browser
 * - Collapsible/expandable
 * - Three user modes (Free, Pro, Developer)
 * - Chat interface with message history
 * - File attachment support
 * - Responsive design
 */
export function ChatbotSidebar() {
  // State for controlling sidebar visibility (collapsed/expanded)
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // State for the current user input
  const [currentMessage, setCurrentMessage] = useState("");
  
  // Mock user mode - in a real app, this would come from user settings/subscription
  const [userMode] = useState<UserMode>("pro");
  
  // State for chat messages with some dummy data for testing
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "1",
      type: "assistant",
      content: "👋 Hi! I'm your AI assistant. Ask me anything about your store, products, or how to optimize your Shopify setup!",
      timestamp: new Date(Date.now() - 300000), // 5 minutes ago
    },
    {
      id: "2",
      type: "user",
      content: "How can I improve my product descriptions?",
      timestamp: new Date(Date.now() - 240000), // 4 minutes ago
    },
    {
      id: "3",
      type: "assistant",
      content: "Great question! Here are some tips for better product descriptions:\n\n• Focus on benefits, not just features\n• Use emotional language that connects with customers\n• Include relevant keywords for SEO\n• Add social proof and testimonials\n• Keep paragraphs short and scannable\n\nWould you like me to analyze any specific products?",
      timestamp: new Date(Date.now() - 180000), // 3 minutes ago
    },
  ]);

  // Ref for the messages container to enable auto-scrolling
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Function to get the appropriate badge color and text for user mode
  const getModeDisplay = (mode: UserMode) => {
    switch (mode) {
      case "free":
        return { tone: "info" as const, text: "🟢 Free" };
      case "pro":
        return { tone: "attention" as const, text: "🟡 Pro" };
      case "developer":
        return { tone: "success" as const, text: "🔵 Developer" };
      default:
        return { tone: "info" as const, text: "🟢 Free" };
    }
  };

  // Function to handle sending a new message
  const handleSendMessage = () => {
    if (!currentMessage.trim()) return;

    // Create new user message
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      type: "user",
      content: currentMessage.trim(),
      timestamp: new Date(),
    };

    // Add user message to chat
    setMessages(prev => [...prev, newUserMessage]);

    // Clear input field
    setCurrentMessage("");

    // Simulate AI response after a short delay
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: "Thanks for your question! I'm processing your request and will provide helpful suggestions based on your store data. This is a demo response - in the real app, I'd analyze your specific store context.",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  // Function to handle Enter key press in input field
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  // Function to handle file attachment (placeholder)
  const handleAttachment = () => {
    // TODO: Implement file attachment functionality
    console.log("File attachment clicked - to be implemented");
  };

  // Function to toggle sidebar collapse/expand
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Get mode display properties
  const modeDisplay = getModeDisplay(userMode);

  return (
    <>
      {/*
        Fixed positioning container that stays on the right side of the browser
        - Uses CSS classes for better styling and responsive behavior
        - position: fixed keeps it in place during scrolling
        - right: 0 positions it at the right edge
        - top: 0 and bottom: 0 make it full height
        - z-index: 1000 ensures it stays above other content
      */}
      <div
        className={`${styles.sidebarContainer} ${
          isCollapsed ? styles.collapsed : styles.expanded
        }`}
      >
        {/* Main sidebar card */}
        <Card>
          <div className={styles.sidebarContent}>
            {/* Header section with mode badge and collapse button */}
            <Box padding="300" className={styles.sidebarHeader}>
              <InlineStack align="space-between" blockAlign="center">
                {!isCollapsed && (
                  <InlineStack gap="200" blockAlign="center">
                    <Icon source={ChatIcon} tone="base" />
                    <Text as="h3" variant="headingSm">
                      AI Assistant
                    </Text>
                    <Badge tone={modeDisplay.tone}>{modeDisplay.text}</Badge>
                  </InlineStack>
                )}
                
                {/* Collapse/Expand toggle button */}
                <Button
                  variant="tertiary"
                  size="slim"
                  icon={isCollapsed ? ChevronLeftIcon : ChevronRightIcon}
                  onClick={toggleSidebar}
                  accessibilityLabel={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                />
              </InlineStack>
            </Box>

            {/* Only show chat content when sidebar is expanded */}
            {!isCollapsed && (
              <>
                {/* Messages area - scrollable container */}
                <div className={styles.messagesContainer}>
                  <Scrollable style={{ height: "100%" }}>
                    <Box padding="300">
                      <BlockStack gap="300">
                        {messages.map((message) => (
                          <div key={message.id}>
                            {/* Message bubble with different styling for user vs assistant */}
                            <Box
                              padding="200"
                              className={`${styles.messageBubble} ${
                                message.type === "user"
                                  ? styles.userMessage
                                  : styles.assistantMessage
                              }`}
                            >
                              <BlockStack gap="100">
                                {/* Message sender label */}
                                <Text 
                                  as="span" 
                                  variant="bodySm" 
                                  tone={message.type === "user" ? "text-inverse" : "subdued"}
                                >
                                  {message.type === "user" ? "You" : "AI Assistant"}
                                </Text>
                                
                                {/* Message content */}
                                <Text 
                                  as="p" 
                                  variant="bodyMd"
                                  tone={message.type === "user" ? "text-inverse" : undefined}
                                  style={{ whiteSpace: "pre-wrap" }}
                                >
                                  {message.content}
                                </Text>
                                
                                {/* Timestamp */}
                                <Text 
                                  as="span" 
                                  variant="bodySm" 
                                  tone={message.type === "user" ? "text-inverse" : "subdued"}
                                >
                                  {message.timestamp.toLocaleTimeString([], { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  })}
                                </Text>
                              </BlockStack>
                            </Box>
                          </div>
                        ))}
                        {/* Invisible div to enable auto-scrolling to bottom */}
                        <div ref={messagesEndRef} />
                      </BlockStack>
                    </Box>
                  </Scrollable>
                </div>

                <Divider />

                {/* Input area for new messages */}
                <Box padding="300" className={styles.inputArea}>
                  <BlockStack gap="200">
                    {/* Text input with attachment and send buttons */}
                    <div style={{ position: "relative" }}>
                      <TextField
                        label=""
                        value={currentMessage}
                        onChange={setCurrentMessage}
                        onKeyPress={handleKeyPress}
                        placeholder="Ask me anything about your store..."
                        multiline={2}
                        autoComplete="off"
                        connectedLeft={
                          <Button
                            variant="tertiary"
                            icon={AttachmentIcon}
                            onClick={handleAttachment}
                            accessibilityLabel="Attach file"
                          />
                        }
                        connectedRight={
                          <Button
                            variant="primary"
                            icon={SendIcon}
                            onClick={handleSendMessage}
                            disabled={!currentMessage.trim()}
                            accessibilityLabel="Send message"
                          />
                        }
                      />
                    </div>
                    
                    {/* Helper text based on user mode */}
                    <Text as="p" variant="bodySm" tone="subdued" alignment="center">
                      {userMode === "free" && "5 messages remaining today"}
                      {userMode === "pro" && "Unlimited messages • Preview & Apply changes"}
                      {userMode === "developer" && "Full access • Debug tools • API access"}
                    </Text>
                  </BlockStack>
                </Box>
              </>
            )}
          </div>
        </Card>
      </div>
    </>
  );
}
