/* 
 * ChatbotSidebar Styles
 * Custom styles to enhance the Polaris components for the chatbot sidebar
 */

/* Fixed sidebar container */
.sidebarContainer {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: width 0.3s ease-in-out;
  pointer-events: auto;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Collapsed state */
.sidebarContainer.collapsed {
  width: 60px;
}

/* Expanded state */
.sidebarContainer.expanded {
  width: 400px;
}

/* Main sidebar content */
.sidebarContent {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--p-color-bg-surface);
  border-left: 1px solid var(--p-color-border);
}

/* Header section */
.sidebarHeader {
  border-bottom: 1px solid var(--p-color-border);
  background: var(--p-color-bg-surface-secondary);
}

/* Messages container */
.messagesContainer {
  flex: 1;
  overflow: hidden;
  background: var(--p-color-bg-surface);
}

/* Individual message bubble */
.messageBubble {
  border-radius: 12px;
  transition: all 0.2s ease-in-out;
}

.messageBubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* User message styling */
.userMessage {
  background: var(--p-color-bg-fill-info);
  color: var(--p-color-text-info-on-bg-fill);
  margin-left: 20px;
  margin-right: 0;
}

/* Assistant message styling */
.assistantMessage {
  background: var(--p-color-bg-surface-secondary);
  color: var(--p-color-text);
  margin-left: 0;
  margin-right: 20px;
}

/* Input area */
.inputArea {
  border-top: 1px solid var(--p-color-border);
  background: var(--p-color-bg-surface);
}

/* Mode badge styling */
.modeBadge {
  font-weight: 600;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .sidebarContainer.expanded {
    width: 100vw;
    left: 0;
  }
  
  .sidebarContainer.collapsed {
    width: 50px;
  }
}

/* Animation for message appearance */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.messageBubble {
  animation: messageSlideIn 0.3s ease-out;
}

/* Typing indicator (for future use) */
.typingIndicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: var(--p-color-bg-surface-secondary);
  border-radius: 12px;
  margin-right: 20px;
}

.typingDot {
  width: 6px;
  height: 6px;
  background: var(--p-color-text-subdued);
  border-radius: 50%;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typingDot:nth-child(1) {
  animation-delay: -0.32s;
}

.typingDot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scrollbar styling for webkit browsers */
.messagesContainer ::-webkit-scrollbar {
  width: 6px;
}

.messagesContainer ::-webkit-scrollbar-track {
  background: var(--p-color-bg-surface);
}

.messagesContainer ::-webkit-scrollbar-thumb {
  background: var(--p-color-border);
  border-radius: 3px;
}

.messagesContainer ::-webkit-scrollbar-thumb:hover {
  background: var(--p-color-border-strong);
}

/* Focus states for accessibility */
.sidebarContainer button:focus {
  outline: 2px solid var(--p-color-border-focus);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .messageBubble {
    border: 1px solid var(--p-color-border-strong);
  }
  
  .userMessage {
    border-color: var(--p-color-border-info);
  }
  
  .assistantMessage {
    border-color: var(--p-color-border);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .sidebarContainer {
    transition: none;
  }
  
  .messageBubble {
    animation: none;
    transition: none;
  }
  
  .typingDot {
    animation: none;
  }
}
