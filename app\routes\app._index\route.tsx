import { useState } from "react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  InlineStack,
  Grid,
  Badge,
  Icon,
  Divider,
  VideoThumbnail,
} from "@shopify/polaris";
import {
  ChatIcon,
  EmailIcon,
  QuestionCircleIcon,
  CheckIcon,
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../../shopify.server";
import styles from "./styles.module.css";

// Export CSS links for Remix to include in the page
export const links = () => [
  { rel: "stylesheet", href: styles },
];

// Loader function - runs on the server before the page loads
// This authenticates the user and returns any data needed for the page
export const loader = async ({ request }: LoaderFunctionArgs) => {
  // Authenticate the admin user with Shopify
  await authenticate.admin(request);

  // For now, we don't need to return any data
  return null;
};

// Main component for the AI Chatbot Assistant homepage
export default function Index() {
  // State to control whether the chat sidebar is open
  const [isChatOpen, setIsChatOpen] = useState(false);

  // Function to open the chat sidebar
  const openChat = () => {
    setIsChatOpen(true);
    // TODO: In a real implementation, this would open the chat sidebar
    console.log("Opening chat sidebar...");
  };

  // Function to handle support chat (placeholder for now)
  const handleSupportChat = () => {
    // TODO: Open support chat system
    console.log("Opening support chat...");
  };

  return (
    <Page>
      {/* Title bar for the app - appears at the top of the Shopify admin */}
      <TitleBar title="AI Assistant for Shopify" />
      
      {/* Main content area with proper spacing */}
      <BlockStack gap="800">
        
        {/* HERO SECTION - Main introduction to the app */}
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="600">
                {/* Hero content with heading and description */}
                <Box padding="600">
                  <BlockStack gap="400" align="center">
                    {/* Main heading */}
                    <Text as="h1" variant="heading2xl" alignment="center">
                      Your AI Assistant for Shopify
                    </Text>
                    
                    {/* Subheading with description */}
                    <Text as="p" variant="bodyLg" alignment="center" tone="subdued">
                      Chat your way to a better store — design, optimize, and manage with ease.
                    </Text>
                    
                    {/* Video placeholder - in a real app, this would be an actual video */}
                    <Box 
                      padding="400" 
                      background="bg-surface-secondary"
                      borderRadius="300"
                      width="100%"
                      maxWidth="600px"
                    >
                      <VideoThumbnail
                        videoLength={120} // 2 minutes
                        thumbnailUrl="https://via.placeholder.com/600x400/E3F2FD/1976D2?text=AI+Assistant+Demo"
                        onClick={() => {
                          // TODO: Open video modal or navigate to video
                          console.log("Playing demo video...");
                        }}
                      />
                      <Box paddingBlockStart="200">
                        <Text as="p" variant="bodySm" alignment="center" tone="subdued">
                          Watch how our AI assistant transforms your store management
                        </Text>
                      </Box>
                    </Box>
                    
                    {/* Call-to-action button */}
                    <Button 
                      variant="primary" 
                      size="large"
                      onClick={openChat}
                      icon={ChatIcon}
                    >
                      {isChatOpen ? "Chat is Open!" : "Get Started - Open Chat"}
                    </Button>
                  </BlockStack>
                </Box>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>

        {/* PLANS SECTION - Show different subscription tiers */}
        <Layout>
          <Layout.Section>
            <BlockStack gap="400">
              {/* Section heading */}
              <Text as="h2" variant="headingXl" alignment="center">
                Choose Your Plan
              </Text>
              <Text as="p" variant="bodyMd" alignment="center" tone="subdued">
                Start free and upgrade as your store grows
              </Text>
              
              {/* Plans grid - responsive layout */}
              <Grid>
                {/* FREE PLAN */}
                <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 2, lg: 4, xl: 4}}>
                  <Card>
                    <BlockStack gap="400">
                      <Box padding="400">
                        <BlockStack gap="300">
                          {/* Plan name and price */}
                          <InlineStack align="space-between" blockAlign="center">
                            <Text as="h3" variant="headingMd">Free</Text>
                            <Badge tone="info">Current</Badge>
                          </InlineStack>
                          <Text as="p" variant="heading2xl">$0</Text>
                          <Text as="p" variant="bodySm" tone="subdued">per month</Text>
                          
                          {/* Plan features */}
                          <BlockStack gap="200">
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">AI suggestions only</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">5 chats per day</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Basic store analysis</Text>
                            </InlineStack>
                          </BlockStack>
                          
                          {/* Plan button */}
                          <Button variant="secondary" fullWidth disabled>
                            Current Plan
                          </Button>
                        </BlockStack>
                      </Box>
                    </BlockStack>
                  </Card>
                </Grid.Cell>

                {/* PRO PLAN */}
                <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 2, lg: 4, xl: 4}}>
                  <Card>
                    <BlockStack gap="400">
                      <Box padding="400">
                        <BlockStack gap="300">
                          {/* Plan name and price with popular badge */}
                          <InlineStack align="space-between" blockAlign="center">
                            <Text as="h3" variant="headingMd">Pro</Text>
                            <Badge tone="attention">Popular</Badge>
                          </InlineStack>
                          <Text as="p" variant="heading2xl">$29</Text>
                          <Text as="p" variant="bodySm" tone="subdued">per month</Text>
                          
                          {/* Plan features */}
                          <BlockStack gap="200">
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Everything in Free</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Unlimited chats</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Preview changes</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Apply changes safely</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Advanced analytics</Text>
                            </InlineStack>
                          </BlockStack>
                          
                          {/* Plan button */}
                          <Button variant="primary" fullWidth>
                            Upgrade to Pro
                          </Button>
                        </BlockStack>
                      </Box>
                    </BlockStack>
                  </Card>
                </Grid.Cell>

                {/* DEVELOPER PLAN */}
                <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 2, lg: 4, xl: 4}}>
                  <Card>
                    <BlockStack gap="400">
                      <Box padding="400">
                        <BlockStack gap="300">
                          {/* Plan name and price */}
                          <Text as="h3" variant="headingMd">Developer</Text>
                          <Text as="p" variant="heading2xl">$99</Text>
                          <Text as="p" variant="bodySm" tone="subdued">per month</Text>
                          
                          {/* Plan features */}
                          <BlockStack gap="200">
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Everything in Pro</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Raw code diffs</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Debug tools</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">API access</Text>
                            </InlineStack>
                            <InlineStack gap="200" blockAlign="center">
                              <Icon source={CheckIcon} tone="success" />
                              <Text as="span" variant="bodySm">Priority support</Text>
                            </InlineStack>
                          </BlockStack>
                          
                          {/* Plan button */}
                          <Button variant="secondary" fullWidth>
                            Upgrade to Developer
                          </Button>
                        </BlockStack>
                      </Box>
                    </BlockStack>
                  </Card>
                </Grid.Cell>
              </Grid>
            </BlockStack>
          </Layout.Section>
        </Layout>

        {/* SUPPORT SECTION - Help and contact information */}
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <Box padding="500">
                  <BlockStack gap="400">
                    {/* Support section heading */}
                    <Text as="h2" variant="headingLg" alignment="center">
                      Need Help? We're Here for You
                    </Text>

                    {/* Support options in a responsive grid */}
                    <Grid>
                      {/* Email Support */}
                      <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 2, lg: 4, xl: 4}}>
                        <Card background="bg-surface-secondary">
                          <Box padding="400">
                            <BlockStack gap="300" align="center">
                              <Icon source={EmailIcon} tone="base" />
                              <Text as="h3" variant="headingMd" alignment="center">
                                Email Support
                              </Text>
                              <Text as="p" variant="bodyMd" alignment="center" tone="subdued">
                                Get help via email within 24 hours
                              </Text>
                              <Text as="p" variant="bodyMd" alignment="center">
                                <EMAIL>
                              </Text>
                              <Button
                                variant="secondary"
                                fullWidth
                                url="mailto:<EMAIL>"
                                external
                              >
                                Send Email
                              </Button>
                            </BlockStack>
                          </Box>
                        </Card>
                      </Grid.Cell>

                      {/* Live Chat Support */}
                      <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 2, lg: 4, xl: 4}}>
                        <Card background="bg-surface-secondary">
                          <Box padding="400">
                            <BlockStack gap="300" align="center">
                              <Icon source={ChatIcon} tone="base" />
                              <Text as="h3" variant="headingMd" alignment="center">
                                Live Chat
                              </Text>
                              <Text as="p" variant="bodyMd" alignment="center" tone="subdued">
                                Chat with our support team in real-time
                              </Text>
                              <Text as="p" variant="bodyMd" alignment="center">
                                Available 9 AM - 6 PM EST
                              </Text>
                              <Button
                                variant="primary"
                                fullWidth
                                onClick={handleSupportChat}
                                icon={ChatIcon}
                              >
                                Chat with Us
                              </Button>
                            </BlockStack>
                          </Box>
                        </Card>
                      </Grid.Cell>

                      {/* Help Documentation */}
                      <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 2, lg: 4, xl: 4}}>
                        <Card background="bg-surface-secondary">
                          <Box padding="400">
                            <BlockStack gap="300" align="center">
                              <Icon source={QuestionCircleIcon} tone="base" />
                              <Text as="h3" variant="headingMd" alignment="center">
                                Help Docs
                              </Text>
                              <Text as="p" variant="bodyMd" alignment="center" tone="subdued">
                                Browse our comprehensive documentation
                              </Text>
                              <Text as="p" variant="bodyMd" alignment="center">
                                FAQs, tutorials, and guides
                              </Text>
                              <Button
                                variant="secondary"
                                fullWidth
                                url="#"
                                external
                              >
                                View Docs
                              </Button>
                            </BlockStack>
                          </Box>
                        </Card>
                      </Grid.Cell>
                    </Grid>

                    {/* Additional support info */}
                    <Divider />
                    <Box paddingBlockStart="400">
                      <Text as="p" variant="bodyMd" alignment="center" tone="subdued">
                        Pro and Developer plan users get priority support with faster response times.
                      </Text>
                    </Box>
                  </BlockStack>
                </Box>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>

        {/* FOOTER SECTION - Additional information and quick links */}
        <Layout>
          <Layout.Section>
            <Card background="bg-surface-secondary">
              <Box padding="500">
                <BlockStack gap="400" align="center">
                  <Text as="h3" variant="headingMd" alignment="center">
                    Ready to Transform Your Store?
                  </Text>
                  <Text as="p" variant="bodyMd" alignment="center" tone="subdued">
                    Join thousands of merchants who are already using AI to optimize their Shopify stores.
                  </Text>
                  <InlineStack gap="300" align="center">
                    <Button
                      variant="primary"
                      size="large"
                      onClick={openChat}
                      icon={ChatIcon}
                    >
                      Start Chatting Now
                    </Button>
                    <Button
                      variant="secondary"
                      size="large"
                      url="#"
                    >
                      Watch Demo
                    </Button>
                  </InlineStack>
                </BlockStack>
              </Box>
            </Card>
          </Layout.Section>
        </Layout>

      </BlockStack>
    </Page>
  );
}
