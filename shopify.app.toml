# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "67eaf61108fd5023cdf4306b561b6313"
name = "AI Store Copilot"
handle = "ai-store-copilot"
application_url = "https://hair-amy-adaptive-attending.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = ["https://hair-amy-adaptive-attending.trycloudflare.com/auth/callback", "https://hair-amy-adaptive-attending.trycloudflare.com/auth/shopify/callback", "https://hair-amy-adaptive-attending.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
