/* 
 * Custom styles for the AI Assistant homepage
 * These styles complement Polaris components with additional customizations
 */

/* Enhanced video section styling */
.videoSection {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease-in-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.videoSection:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

/* YouTube iframe responsive container */
.videoWrapper {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 12px;
  background: #000;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 12px;
}

/* Video loading state */
.videoWrapper::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: videoLoading 1s linear infinite;
  z-index: 1;
}

@keyframes videoLoading {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Video feature highlights */
.videoFeatures {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin-top: 1rem;
}

.featureHighlight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease-in-out;
}

.featureHighlight:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

/* Video stats styling */
.videoStats {
  text-align: center;
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Plan cards hover effects */
.planCard {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  height: 100%;
}

.planCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

/* Popular plan highlight */
.popularPlan {
  position: relative;
  border: 2px solid #2563eb;
}

.popularPlan::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  border-radius: 8px;
  z-index: -1;
}

/* Support section cards */
.supportCard {
  transition: transform 0.2s ease-in-out;
  height: 100%;
}

.supportCard:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .videoSection {
    max-width: 100%;
    margin: 0 0.5rem;
    border-radius: 12px;
  }

  .videoWrapper {
    border-radius: 8px;
  }

  .videoFeatures {
    flex-direction: column;
    gap: 0.75rem;
  }

  .featureHighlight {
    width: 100%;
    justify-content: center;
    padding: 0.75rem 1rem;
  }

  .planCard {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .videoSection {
    margin: 0 0.25rem;
    padding: 1rem;
  }

  .videoWrapper {
    border-radius: 6px;
  }

  .videoStats {
    font-size: 0.875rem;
    padding: 0.5rem;
  }
}

/* Custom button styles for CTAs */
.primaryCta {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  transition: all 0.2s ease-in-out;
}

.primaryCta:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
  transform: translateY(-1px);
}

/* Feature list styling */
.featureList {
  list-style: none;
  padding: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Accessibility improvements */
.skipLink {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skipLink:focus {
  top: 6px;
}

/* Focus indicators for better accessibility */
.focusable:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Animation for chat button */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
  }
}

.chatButton {
  animation: pulse 2s infinite;
}

/* Grid layout improvements */
.responsiveGrid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 640px) {
  .responsiveGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
